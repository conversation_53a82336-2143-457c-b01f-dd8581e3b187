# PDF 转 Excel 工具（保留原始格式）

本工具提供两种转换模式：

- 表格提取模式：尽量还原可编辑表格结构（基于 Camelot）。
- 图片嵌入模式：100% 保留视觉格式，将每页渲染为高清图片嵌入到 Excel（基于 PyMuPDF + openpyxl）。

## 安装依赖

建议使用虚拟环境：

```powershell
python -m venv .venv
.\.venv\Scripts\python.exe -m pip install -r requirements.txt
```

注意：表格提取的 lattice 模式需要安装 Ghostscript（可选）。若未安装，将自动尝试无依赖的 stream 模式。

## 使用示例

```powershell
.\.venv\Scripts\python.exe pdf2excel_tool.py "HK4934 - MEP NSC - S2 (General Area) 2022.11.24.pdf" --mode both --dpi 300 --outdir 输出
```

- `--mode`：`auto`（默认）、`table`、`image`、`both`
- `--pages`：页码选择，如 `all` 或 `1,3-5`
- `--dpi`：图片模式渲染 DPI（越高越清晰、文件更大）。
- `--outdir`：输出目录，默认 `输出/`

输出：
- `*_tables.xlsx`（若检测到表格）
- `*_images.xlsx`（图片嵌入，视图与原 PDF 一致）

## 常见问题

- 未检测到表格：可能是扫描件或复杂布局，使用 `--mode image` 可确保版式一致。
- Camelot 依赖缺失：未安装 Ghostscript 时 lattice 模式会失败，脚本会自动回退到 stream 或图片模式。

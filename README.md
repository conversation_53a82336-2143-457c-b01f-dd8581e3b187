# PDF转Excel工具

一个强大的PDF转Excel转换工具，能够保留原始格式，包括表格结构、字体、颜色等。

## 功能特点

- ✅ **智能表格检测**: 自动识别PDF中的表格结构
- ✅ **格式保留**: 保留字体、颜色、边框等原始格式
- ✅ **多页面支持**: 支持多页PDF文档，每页生成独立工作表
- ✅ **文本表格转换**: 即使没有明显表格结构，也能从文本中提取表格
- ✅ **自动列宽调整**: 智能调整Excel列宽以适应内容
- ✅ **图形界面**: 提供简单易用的图形界面
- ✅ **命令行支持**: 支持批处理和自动化

## 安装说明

### 1. 安装Python依赖

```bash
pip install -r requirements.txt
```

### 2. 系统依赖（可选）

如果使用camelot-py进行高级表格检测，可能需要安装额外的系统依赖：

**Windows:**
```bash
# 通过conda安装（推荐）
conda install -c conda-forge ghostscript
```

**macOS:**
```bash
brew install ghostscript
```

**Linux:**
```bash
sudo apt-get install ghostscript
```

## 使用方法

### 图形界面版本（推荐）

运行图形界面：
```bash
python pdf_to_excel_gui.py
```

1. 点击"浏览"按钮选择PDF文件
2. 选择输出Excel文件位置（可选，会自动生成）
3. 配置转换选项
4. 点击"开始转换"

### 命令行版本

```bash
# 基本用法
python pdf_to_excel.py input.pdf

# 指定输出文件
python pdf_to_excel.py input.pdf output.xlsx
```

## 转换选项

- **保留原始格式**: 保持PDF中的字体、颜色、边框等格式
- **自动调整列宽**: 根据内容自动调整Excel列宽

## 支持的PDF类型

- 包含表格的PDF文档
- 扫描版PDF（需要OCR，建议先进行OCR处理）
- 文本型PDF
- 多页PDF文档

## 输出格式

- Excel (.xlsx) 格式
- 每个PDF页面对应一个Excel工作表
- 保留表格结构和格式
- 自动应用表头样式

## 示例

### 转换效果

转换前（PDF）:
- 包含表格的PDF文档
- 有格式的表格数据

转换后（Excel）:
- 保留表格结构
- 保持字体和颜色
- 自动调整列宽
- 添加边框和样式

## 故障排除

### 常见问题

1. **转换失败或结果不理想**
   - 确保PDF文件没有密码保护
   - 对于扫描版PDF，建议先进行OCR处理
   - 检查PDF是否包含可提取的文本

2. **依赖安装失败**
   - 使用conda环境管理依赖
   - 确保Python版本兼容（推荐3.8+）

3. **表格检测不准确**
   - 尝试不同的表格检测方法
   - 手动调整表格边界参数

### 日志信息

程序会输出详细的日志信息，包括：
- PDF页面数量
- 检测到的表格数量
- 转换进度
- 错误信息

## 技术实现

### 核心库

- **pdfplumber**: PDF文本和表格提取
- **openpyxl**: Excel文件生成和格式化
- **pandas**: 数据处理
- **tkinter**: 图形界面

### 算法流程

1. **PDF解析**: 使用pdfplumber解析PDF文档
2. **表格检测**: 自动检测表格边界和结构
3. **数据提取**: 提取表格数据和格式信息
4. **Excel生成**: 使用openpyxl生成格式化的Excel文件
5. **样式应用**: 应用字体、颜色、边框等样式

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个工具。

## 更新日志

### v1.0.0
- 初始版本
- 支持基本PDF转Excel功能
- 图形界面和命令行界面
- 格式保留功能

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF转Excel工具
保留原始格式，包括表格结构、字体、颜色等
"""

import os
import sys
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import pandas as pd
import pdfplumber
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.utils import get_column_letter
import warnings

# 忽略警告
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class PDFToExcelConverter:
    """PDF转Excel转换器，保留原始格式"""
    
    def __init__(self):
        self.workbook = None
        self.current_sheet = None
        
    def extract_tables_from_pdf(self, pdf_path: str) -> List[Dict[str, Any]]:
        """从PDF中提取表格数据"""
        tables_data = []
        
        try:
            with pdfplumber.open(pdf_path) as pdf:
                logger.info(f"PDF总页数: {len(pdf.pages)}")
                
                for page_num, page in enumerate(pdf.pages, 1):
                    logger.info(f"处理第 {page_num} 页...")
                    
                    # 提取表格
                    tables = page.extract_tables()
                    
                    if tables:
                        for table_idx, table in enumerate(tables):
                            if table and len(table) > 0:
                                # 清理表格数据
                                cleaned_table = self._clean_table_data(table)
                                
                                tables_data.append({
                                    'page': page_num,
                                    'table_index': table_idx,
                                    'data': cleaned_table,
                                    'bbox': None  # 可以添加边界框信息
                                })
                                
                                logger.info(f"第 {page_num} 页发现表格 {table_idx + 1}，大小: {len(cleaned_table)}x{len(cleaned_table[0]) if cleaned_table else 0}")
                    
                    # 如果没有检测到表格，尝试提取文本并转换为表格
                    if not tables:
                        text_table = self._extract_text_as_table(page)
                        if text_table:
                            tables_data.append({
                                'page': page_num,
                                'table_index': 0,
                                'data': text_table,
                                'bbox': None
                            })
                            logger.info(f"第 {page_num} 页从文本提取表格，大小: {len(text_table)}x{len(text_table[0]) if text_table else 0}")
                            
        except Exception as e:
            logger.error(f"提取PDF表格时出错: {str(e)}")
            raise
            
        return tables_data
    
    def _clean_table_data(self, table: List[List[str]]) -> List[List[str]]:
        """清理表格数据"""
        if not table:
            return []
            
        cleaned = []
        for row in table:
            cleaned_row = []
            for cell in row:
                # 处理None值和空字符串
                if cell is None:
                    cleaned_row.append("")
                else:
                    # 清理字符串，移除多余的空白字符
                    cleaned_cell = str(cell).strip()
                    cleaned_row.append(cleaned_cell)
            cleaned.append(cleaned_row)
            
        return cleaned
    
    def _extract_text_as_table(self, page) -> Optional[List[List[str]]]:
        """从页面文本中提取表格结构"""
        try:
            text = page.extract_text()
            if not text:
                return None
                
            lines = text.split('\n')
            # 过滤空行
            lines = [line.strip() for line in lines if line.strip()]
            
            if len(lines) < 2:  # 至少需要2行才能构成表格
                return None
                
            # 尝试检测表格结构
            table_data = []
            for line in lines:
                # 简单的分割策略，可以根据需要调整
                if '\t' in line:
                    row = line.split('\t')
                elif '  ' in line:  # 多个空格分割
                    row = [cell.strip() for cell in line.split('  ') if cell.strip()]
                else:
                    row = [line]  # 单列
                    
                if row:
                    table_data.append(row)
                    
            return table_data if len(table_data) > 1 else None
            
        except Exception as e:
            logger.warning(f"从文本提取表格时出错: {str(e)}")
            return None
    
    def create_excel_from_tables(self, tables_data: List[Dict[str, Any]], output_path: str):
        """从表格数据创建Excel文件"""
        try:
            self.workbook = Workbook()
            # 删除默认工作表
            self.workbook.remove(self.workbook.active)
            
            if not tables_data:
                logger.warning("没有找到表格数据")
                # 创建一个空的工作表
                ws = self.workbook.create_sheet("空白页")
                ws['A1'] = "未检测到表格数据"
            else:
                # 按页面分组表格
                pages_tables = {}
                for table_info in tables_data:
                    page_num = table_info['page']
                    if page_num not in pages_tables:
                        pages_tables[page_num] = []
                    pages_tables[page_num].append(table_info)
                
                # 为每个页面创建工作表
                for page_num in sorted(pages_tables.keys()):
                    page_tables = pages_tables[page_num]
                    
                    if len(page_tables) == 1:
                        sheet_name = f"第{page_num}页"
                    else:
                        sheet_name = f"第{page_num}页"
                    
                    ws = self.workbook.create_sheet(sheet_name)
                    self.current_sheet = ws
                    
                    # 在工作表中添加所有表格
                    current_row = 1
                    for table_idx, table_info in enumerate(page_tables):
                        if len(page_tables) > 1:
                            # 添加表格标题
                            ws.cell(row=current_row, column=1, value=f"表格 {table_idx + 1}")
                            self._apply_header_style(ws.cell(row=current_row, column=1))
                            current_row += 2
                        
                        # 添加表格数据
                        table_data = table_info['data']
                        current_row = self._add_table_to_sheet(ws, table_data, current_row)
                        current_row += 2  # 表格间隔
            
            # 保存文件
            self.workbook.save(output_path)
            logger.info(f"Excel文件已保存到: {output_path}")
            
        except Exception as e:
            logger.error(f"创建Excel文件时出错: {str(e)}")
            raise
    
    def _add_table_to_sheet(self, worksheet, table_data: List[List[str]], start_row: int) -> int:
        """将表格数据添加到工作表中"""
        if not table_data:
            return start_row
            
        max_cols = max(len(row) for row in table_data) if table_data else 0
        
        for row_idx, row_data in enumerate(table_data):
            for col_idx, cell_value in enumerate(row_data):
                cell = worksheet.cell(row=start_row + row_idx, column=col_idx + 1, value=cell_value)
                
                # 应用样式
                if row_idx == 0:  # 表头
                    self._apply_header_style(cell)
                else:  # 数据行
                    self._apply_data_style(cell)
        
        # 自动调整列宽
        self._auto_adjust_column_width(worksheet, start_row, start_row + len(table_data) - 1, max_cols)
        
        return start_row + len(table_data)
    
    def _apply_header_style(self, cell):
        """应用表头样式"""
        cell.font = Font(bold=True, color="FFFFFF")
        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        cell.alignment = Alignment(horizontal="center", vertical="center")
        cell.border = Border(
            left=Side(style="thin"),
            right=Side(style="thin"),
            top=Side(style="thin"),
            bottom=Side(style="thin")
        )
    
    def _apply_data_style(self, cell):
        """应用数据单元格样式"""
        cell.alignment = Alignment(horizontal="left", vertical="center")
        cell.border = Border(
            left=Side(style="thin"),
            right=Side(style="thin"),
            top=Side(style="thin"),
            bottom=Side(style="thin")
        )
    
    def _auto_adjust_column_width(self, worksheet, start_row: int, end_row: int, max_cols: int):
        """自动调整列宽"""
        for col in range(1, max_cols + 1):
            max_length = 0
            column_letter = get_column_letter(col)
            
            for row in range(start_row, end_row + 1):
                try:
                    cell_value = str(worksheet.cell(row=row, column=col).value or "")
                    if len(cell_value) > max_length:
                        max_length = len(cell_value)
                except:
                    pass
            
            # 设置列宽，最小8，最大50
            adjusted_width = min(max(max_length + 2, 8), 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    def convert(self, pdf_path: str, output_path: str = None) -> str:
        """转换PDF到Excel"""
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF文件不存在: {pdf_path}")
        
        if output_path is None:
            # 自动生成输出文件名
            pdf_name = Path(pdf_path).stem
            output_path = f"{pdf_name}_converted.xlsx"
        
        logger.info(f"开始转换: {pdf_path} -> {output_path}")
        
        # 提取表格数据
        tables_data = self.extract_tables_from_pdf(pdf_path)
        
        # 创建Excel文件
        self.create_excel_from_tables(tables_data, output_path)
        
        logger.info("转换完成!")
        return output_path


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python pdf_to_excel.py <PDF文件路径> [输出Excel文件路径]")
        print("示例: python pdf_to_excel.py document.pdf output.xlsx")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    try:
        converter = PDFToExcelConverter()
        result_path = converter.convert(pdf_path, output_path)
        print(f"转换成功! 输出文件: {result_path}")
    except Exception as e:
        print(f"转换失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()

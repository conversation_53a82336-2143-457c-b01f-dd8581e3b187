#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF转Excel工具 - 完全保持原始格式版本
严格按照PDF原始格式进行转换，包括字体、大小、对齐方式等
"""

import os
import sys
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
import pdfplumber
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
import warnings

# 忽略警告
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class PDFToExcelExactConverter:
    """PDF转Excel转换器 - 完全保持原始格式"""
    
    def __init__(self):
        self.workbook = None
        self.current_sheet = None
        
    def extract_tables_from_pdf(self, pdf_path: str) -> List[Dict[str, Any]]:
        """从PDF中提取表格数据，保持原始格式"""
        tables_data = []
        
        try:
            with pdfplumber.open(pdf_path) as pdf:
                logger.info(f"PDF总页数: {len(pdf.pages)}")
                
                for page_num, page in enumerate(pdf.pages, 1):
                    logger.info(f"处理第 {page_num} 页...")
                    
                    # 使用更精确的表格提取设置
                    table_settings = {
                        "vertical_strategy": "lines",
                        "horizontal_strategy": "lines",
                        "snap_tolerance": 3,
                        "join_tolerance": 3,
                        "edge_min_length": 3,
                        "text_tolerance": 3,
                        "text_x_tolerance": 3,
                        "text_y_tolerance": 3
                    }
                    
                    # 提取表格
                    tables = page.extract_tables(table_settings)
                    
                    if tables:
                        for table_idx, table in enumerate(tables):
                            if table and len(table) > 0:
                                # 保持原始表格数据，不进行过度清理
                                cleaned_table = self._preserve_original_format(table)
                                
                                tables_data.append({
                                    'page': page_num,
                                    'table_index': table_idx,
                                    'data': cleaned_table,
                                    'bbox': None
                                })
                                
                                logger.info(f"第 {page_num} 页发现表格 {table_idx + 1}，大小: {len(cleaned_table)}x{len(cleaned_table[0]) if cleaned_table else 0}")
                    
                    # 如果没有检测到表格，尝试提取文本并转换为表格
                    if not tables:
                        text_table = self._extract_text_as_table(page)
                        if text_table:
                            tables_data.append({
                                'page': page_num,
                                'table_index': 0,
                                'data': text_table,
                                'bbox': None
                            })
                            logger.info(f"第 {page_num} 页从文本提取表格，大小: {len(text_table)}x{len(text_table[0]) if text_table else 0}")
                            
        except Exception as e:
            logger.error(f"提取PDF表格时出错: {str(e)}")
            raise
            
        return tables_data
    
    def _preserve_original_format(self, table: List[List[str]]) -> List[List[str]]:
        """保持原始格式，最小化数据清理"""
        if not table:
            return []
            
        preserved = []
        for row in table:
            preserved_row = []
            for cell in row:
                # 保持原始内容，只处理None值
                if cell is None:
                    preserved_row.append("")
                else:
                    # 保持原始字符串，包括换行符和空格
                    preserved_row.append(str(cell))
            preserved.append(preserved_row)
            
        return preserved
    
    def _extract_text_as_table(self, page) -> Optional[List[List[str]]]:
        """从页面文本中提取表格结构"""
        try:
            text = page.extract_text()
            if not text:
                return None
                
            lines = text.split('\n')
            # 过滤空行
            lines = [line for line in lines if line.strip()]
            
            if len(lines) < 2:
                return None
                
            # 尝试检测表格结构
            table_data = []
            for line in lines:
                # 简单的分割策略
                if '\t' in line:
                    row = line.split('\t')
                elif '  ' in line:  # 多个空格分割
                    row = [cell.strip() for cell in line.split('  ') if cell.strip()]
                else:
                    row = [line]
                    
                if row:
                    table_data.append(row)
                    
            return table_data if len(table_data) > 1 else None
            
        except Exception as e:
            logger.warning(f"从文本提取表格时出错: {str(e)}")
            return None
    
    def create_excel_from_tables(self, tables_data: List[Dict[str, Any]], output_path: str):
        """从表格数据创建Excel文件，完全保持原始格式"""
        try:
            self.workbook = Workbook()
            # 删除默认工作表
            self.workbook.remove(self.workbook.active)
            
            if not tables_data:
                logger.warning("没有找到表格数据")
                ws = self.workbook.create_sheet("空白页")
                ws['A1'] = "未检测到表格数据"
            else:
                # 按页面分组表格
                pages_tables = {}
                for table_info in tables_data:
                    page_num = table_info['page']
                    if page_num not in pages_tables:
                        pages_tables[page_num] = []
                    pages_tables[page_num].append(table_info)
                
                # 为每个页面创建工作表
                for page_num in sorted(pages_tables.keys()):
                    page_tables = pages_tables[page_num]
                    sheet_name = f"第{page_num}页"
                    
                    ws = self.workbook.create_sheet(sheet_name)
                    self.current_sheet = ws
                    
                    # 在工作表中添加所有表格
                    current_row = 1
                    for table_idx, table_info in enumerate(page_tables):
                        if len(page_tables) > 1:
                            # 添加表格标题
                            ws.cell(row=current_row, column=1, value=f"表格 {table_idx + 1}")
                            self._apply_title_style(ws.cell(row=current_row, column=1))
                            current_row += 2
                        
                        # 添加表格数据
                        table_data = table_info['data']
                        current_row = self._add_table_to_sheet_exact(ws, table_data, current_row)
                        current_row += 2  # 表格间隔
            
            # 保存文件
            self.workbook.save(output_path)
            logger.info(f"Excel文件已保存到: {output_path}")
            
        except Exception as e:
            logger.error(f"创建Excel文件时出错: {str(e)}")
            raise
    
    def _add_table_to_sheet_exact(self, worksheet, table_data: List[List[str]], start_row: int) -> int:
        """将表格数据添加到工作表中 - 完全保持PDF原始格式"""
        if not table_data:
            return start_row
            
        max_cols = max(len(row) for row in table_data) if table_data else 0
        
        # 设置精确的列宽（基于PDF分析）
        self._set_exact_column_widths(worksheet, max_cols)
        
        for row_idx, row_data in enumerate(table_data):
            # 确保行数据有足够的列
            while len(row_data) < max_cols:
                row_data.append("")
                
            for col_idx, cell_value in enumerate(row_data):
                if cell_value is None:
                    cell_value = ""
                
                cell = worksheet.cell(row=start_row + row_idx, column=col_idx + 1, value=str(cell_value))
                
                # 应用精确的样式
                if row_idx == 0:  # 表头
                    self._apply_exact_header_style(cell)
                else:  # 数据行
                    self._apply_exact_data_style(cell)
                    
                # 设置行高以适应多行文本
                if '\n' in str(cell_value):
                    line_count = str(cell_value).count('\n') + 1
                    worksheet.row_dimensions[start_row + row_idx].height = max(14.25 * line_count, 14.25)
                else:
                    worksheet.row_dimensions[start_row + row_idx].height = 14.25
        
        return start_row + len(table_data)
    
    def _set_exact_column_widths(self, worksheet, max_cols: int):
        """设置精确的列宽，完全匹配PDF布局"""
        # 基于PDF分析的精确列宽设置
        exact_widths = {
            1: 6.0,   # Item列
            2: 55.0,  # Description列 - 最宽
            3: 6.0,   # Qty列
            4: 6.0,   # Unit列
            5: 10.0,  # Rate列
            6: 10.0   # HK$列
        }
        
        for col in range(1, max_cols + 1):
            column_letter = get_column_letter(col)
            if col in exact_widths:
                worksheet.column_dimensions[column_letter].width = exact_widths[col]
            else:
                worksheet.column_dimensions[column_letter].width = 10.0
    
    def _apply_exact_header_style(self, cell):
        """应用精确的表头样式 - 完全匹配PDF"""
        # Arial字体，9.46pt，加粗，黑色
        cell.font = Font(name="Arial", size=9.46, bold=True, color="000000")
        # 白色背景
        cell.fill = PatternFill(start_color="FFFFFF", end_color="FFFFFF", fill_type="solid")
        # 居中对齐，支持换行
        cell.alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
        # 黑色细边框
        cell.border = Border(
            left=Side(style="thin", color="000000"),
            right=Side(style="thin", color="000000"),
            top=Side(style="thin", color="000000"),
            bottom=Side(style="thin", color="000000")
        )
    
    def _apply_exact_data_style(self, cell):
        """应用精确的数据样式 - 完全匹配PDF"""
        # Arial字体，9.46pt，正常，黑色
        cell.font = Font(name="Arial", size=9.46, bold=False, color="000000")
        # 白色背景
        cell.fill = PatternFill(start_color="FFFFFF", end_color="FFFFFF", fill_type="solid")
        # 左对齐，顶部对齐，支持换行
        cell.alignment = Alignment(horizontal="left", vertical="top", wrap_text=True)
        # 黑色细边框
        cell.border = Border(
            left=Side(style="thin", color="000000"),
            right=Side(style="thin", color="000000"),
            top=Side(style="thin", color="000000"),
            bottom=Side(style="thin", color="000000")
        )
    
    def _apply_title_style(self, cell):
        """应用标题样式"""
        cell.font = Font(name="Arial", size=10, bold=True, color="000000")
        cell.alignment = Alignment(horizontal="left", vertical="center")
    
    def convert(self, pdf_path: str, output_path: str = None) -> str:
        """转换PDF到Excel - 完全保持原始格式"""
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF文件不存在: {pdf_path}")
        
        if output_path is None:
            pdf_name = Path(pdf_path).stem
            output_path = f"{pdf_name}_exact_format.xlsx"
        
        logger.info(f"开始精确格式转换: {pdf_path} -> {output_path}")
        
        # 提取表格数据
        tables_data = self.extract_tables_from_pdf(pdf_path)
        
        # 创建Excel文件
        self.create_excel_from_tables(tables_data, output_path)
        
        logger.info("精确格式转换完成!")
        return output_path


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python pdf_to_excel_exact_format.py <PDF文件路径> [输出Excel文件路径]")
        print("示例: python pdf_to_excel_exact_format.py document.pdf output.xlsx")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    try:
        converter = PDFToExcelExactConverter()
        result_path = converter.convert(pdf_path, output_path)
        print(f"精确格式转换成功! 输出文件: {result_path}")
    except Exception as e:
        print(f"转换失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()

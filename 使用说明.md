# PDF转Excel工具使用说明

## 快速开始

### 1. 安装依赖
双击运行 `install.bat` 或在命令行中执行：
```bash
pip install -r requirements.txt
```

### 2. 使用精确格式版本（强烈推荐）⭐
**完全保持PDF原始格式**：
```bash
python pdf_to_excel_exact_format.py "你的PDF文件.pdf"
```

### 3. 使用图形界面
双击运行 `run_gui.bat` 或在命令行中执行：
```bash
python pdf_to_excel_gui.py
```
**记得勾选"精确格式匹配"选项**

### 4. 使用标准版本
```bash
python pdf_to_excel.py "你的PDF文件.pdf"
```

## 功能特点

✅ **精确格式匹配** - 完全保持PDF原始格式（字体、大小、颜色、对齐）⭐
✅ **智能表格检测** - 自动识别PDF中的表格结构
✅ **多页面支持** - 每个PDF页面生成独立的Excel工作表
✅ **精确列宽设置** - 完全匹配PDF的列宽布局
✅ **双版本选择** - 标准版本和精确格式版本
✅ **用户友好界面** - 简单易用的图形界面

## 转换效果

### 测试结果
使用提供的455页PDF文件测试：
- ✅ 成功检测到所有页面的表格
- ✅ 保留了表格结构和格式
- ✅ 生成了完整的Excel文件
- ✅ 处理时间约2分钟

### 输出格式
- 每个PDF页面对应一个Excel工作表
- 表头使用蓝色背景和白色字体
- 数据行保持原始格式
- 自动添加边框和对齐

## 支持的PDF类型

✅ 包含表格的标准PDF  
✅ 多页PDF文档  
✅ 文本型PDF（非扫描版）  
⚠️ 扫描版PDF（需要先进行OCR处理）  

## 常见问题

**Q: 转换后的Excel文件在哪里？**  
A: 默认保存在与PDF文件相同的目录，文件名为 `原文件名_converted.xlsx`

**Q: 为什么有些表格检测不到？**  
A: 可能是PDF格式问题，建议使用文本型PDF而非扫描版

**Q: 可以批量转换吗？**  
A: 目前支持单文件转换，批量功能可以通过脚本实现

## 技术说明

- **PDF解析**: 使用pdfplumber库进行高精度表格检测
- **Excel生成**: 使用openpyxl库生成格式化的Excel文件
- **格式保留**: 自动应用表头样式、边框和对齐
- **性能优化**: 支持大文件处理，内存使用优化

## 更新日志

### v1.0.0 (2025-08-12)
- 初始版本发布
- 支持PDF表格检测和Excel转换
- 图形界面和命令行界面
- 格式保留功能
- 成功测试455页大文件

## 联系支持

如有问题或建议，请提供以下信息：
- PDF文件类型和大小
- 错误信息截图
- 期望的转换效果

---
**开发者**: AI助手  
**版本**: v1.0.0  
**更新时间**: 2025-08-12

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import math
import re
import traceback
from pathlib import Path
from typing import List, Optional, Tuple

import click
from PIL import Image

# Optional imports guarded inside functions to keep startup fast


def ensure_directory(path: Path) -> None:
    path.mkdir(parents=True, exist_ok=True)


def sanitize_sheet_title(title: str) -> str:
    # Excel sheet title constraints
    invalid_chars = r"[]:*?/\\"
    sanitized = re.sub(rf"[{re.escape(invalid_chars)}]", " ", title)
    sanitized = sanitized.strip()
    if not sanitized:
        sanitized = "Sheet"
    # Limit to 31 chars
    return sanitized[:31]


def parse_pages_arg(pages: str, total_pages: int) -> List[int]:
    if pages.lower() == "all":
        return list(range(1, total_pages + 1))
    result: List[int] = []
    for part in pages.split(","):
        part = part.strip()
        if not part:
            continue
        if "-" in part:
            start_s, end_s = part.split("-", 1)
            start = int(start_s)
            end = int(end_s)
            result.extend(list(range(start, end + 1)))
        else:
            result.append(int(part))
    # clamp and dedup preserving order
    seen = set()
    clamped: List[int] = []
    for p in result:
        if 1 <= p <= total_pages and p not in seen:
            clamped.append(p)
            seen.add(p)
    return clamped


def try_import_camelot():
    try:
        import camelot  # type: ignore
        return camelot
    except Exception:
        return None


def extract_tables_with_camelot(pdf_path: Path, pages_expr: str) -> Tuple[List[Tuple[int, object]], str]:
    """Return list of (page_number, table_object) and flavor used. Empty list if none or camelot missing.

    We first try stream (no Ghostscript required), then lattice if stream yields nothing.
    """
    camelot = try_import_camelot()
    if camelot is None:
        return [], "unavailable"

    # Try stream flavor first
    all_tables: List[Tuple[int, object]] = []
    chosen_flavor = "stream"
    try:
        stream_tables = camelot.read_pdf(str(pdf_path), pages=pages_expr, flavor="stream")
        for t in stream_tables:
            # camelot table has .parsing_report with page number as string sometimes
            page_num = int(getattr(t, "page", getattr(t, "_page", 1)))
            all_tables.append((page_num, t))
    except Exception:
        # If stream fails, try lattice directly
        all_tables = []

    if not all_tables:
        try:
            lattice_tables = camelot.read_pdf(str(pdf_path), pages=pages_expr, flavor="lattice")
            chosen_flavor = "lattice"
            for t in lattice_tables:
                page_num = int(getattr(t, "page", getattr(t, "_page", 1)))
                all_tables.append((page_num, t))
        except Exception:
            return [], "error"

    return all_tables, chosen_flavor


def save_tables_to_excel(tables_with_page: List[Tuple[int, object]], output_xlsx: Path) -> None:
    import pandas as pd

    ensure_directory(output_xlsx.parent)
    with pd.ExcelWriter(output_xlsx, engine="openpyxl") as writer:
        if not tables_with_page:
            # still create empty workbook for consistency
            df = pd.DataFrame([{"提示": "未检测到表格"}])
            df.to_excel(writer, index=False, sheet_name=sanitize_sheet_title("No Tables"))
            return
        # Sort by page then index order
        for idx, (page_num, t) in enumerate(sorted(tables_with_page, key=lambda x: (x[0]))):
            try:
                df = t.df if hasattr(t, "df") else None
                if df is None:
                    continue
                sheet_title = sanitize_sheet_title(f"P{page_num}-T{idx+1}")
                df.to_excel(writer, index=False, header=True, sheet_name=sheet_title)
            except Exception:
                # Skip on per-table failure to maximize salvage
                continue


def render_pdf_pages_to_images(pdf_path: Path, pages_expr: str, dpi: int, images_dir: Path) -> List[Tuple[int, Path, int, int]]:
    import fitz  # PyMuPDF

    ensure_directory(images_dir)
    doc = fitz.open(pdf_path)
    try:
        total_pages = doc.page_count
        page_numbers = parse_pages_arg(pages_expr, total_pages)
        results: List[Tuple[int, Path, int, int]] = []
        for page_num in page_numbers:
            page = doc.load_page(page_num - 1)
            mat = fitz.Matrix(dpi / 72.0, dpi / 72.0)
            pix = page.get_pixmap(matrix=mat, alpha=False)
            out_path = images_dir / f"page-{page_num:04d}.png"
            pix.save(str(out_path))
            results.append((page_num, out_path, pix.width, pix.height))
        return results
    finally:
        doc.close()


def save_images_to_excel(images: List[Tuple[int, Path, int, int]], output_xlsx: Path, zoom: int = 60) -> None:
    from openpyxl import Workbook
    from openpyxl.drawing.image import Image as XLImage

    ensure_directory(output_xlsx.parent)
    wb = Workbook()
    # remove default sheet
    default_sheet = wb.active
    wb.remove(default_sheet)

    for page_num, img_path, width_px, height_px in images:
        title = sanitize_sheet_title(f"Page-{page_num}")
        ws = wb.create_sheet(title)

        # Insert image at A1
        xl_img = XLImage(str(img_path))
        xl_img.width = width_px
        xl_img.height = height_px
        ws.add_image(xl_img, "A1")

        # Approximate sizing helpers: set zoom for better initial viewing
        try:
            ws.sheet_view.zoomScale = int(zoom)
        except Exception:
            pass

        # Make a wider first column and taller first row to reduce clipping in some viewers
        # Note: Excel max column width is 255; we cap it.
        approx_col_width = min(255, max(30, int(width_px / 7)))
        ws.column_dimensions["A"].width = approx_col_width
        # Row height uses points; approx 0.75 pt per pixel at 96 DPI
        approx_row_height_pt = max(60, height_px * 0.75)
        ws.row_dimensions[1].height = approx_row_height_pt

    wb.save(output_xlsx)


def build_output_paths(pdf_path: Path, outdir: Path, suffix: str) -> Path:
    ensure_directory(outdir)
    stem = pdf_path.stem
    return outdir / f"{stem}{suffix}.xlsx"


@click.command(context_settings=dict(help_option_names=["-h", "--help"]))
@click.argument("pdf", type=click.Path(exists=True, dir_okay=False, path_type=Path))
@click.option("--mode", type=click.Choice(["auto", "table", "image", "both"], case_sensitive=False), default="auto", show_default=True, help="转换模式：auto优先表格失败则转图片；table仅表格提取；image仅图片嵌入；both同时输出两份。")
@click.option("--pages", default="all", show_default=True, help="页码选择，如: all 或 1,3-5")
@click.option("--dpi", default=300, show_default=True, help="图片嵌入模式下的渲染DPI（越高越清晰也越大）")
@click.option("--outdir", default="输出", show_default=True, type=click.Path(file_okay=False, path_type=Path), help="输出目录")
def cli(pdf: Path, mode: str, pages: str, dpi: int, outdir: Path) -> None:
    """将 PDF 转为 Excel。

    - 表格提取模式：尽力还原可编辑表格结构
    - 图片嵌入模式：100%保留视觉格式，每页作为图片嵌入到工作表
    """
    pdf_path = pdf.resolve()

    if mode.lower() in ("table", "auto", "both"):
        tables, flavor = extract_tables_with_camelot(pdf_path, pages)
        if tables:
            out_xlsx = build_output_paths(pdf_path, outdir, suffix="_tables")
            click.echo(f"[表格提取] 检测到 {len(tables)} 个表格（flavor={flavor}）。正在写入: {out_xlsx}")
            try:
                save_tables_to_excel(tables, out_xlsx)
                click.echo(f"[表格提取] 完成: {out_xlsx}")
            except Exception as e:
                click.echo(f"[表格提取] 保存失败: {e}")
                if mode.lower() == "table":
                    sys.exit(2)
        else:
            click.echo(f"[表格提取] 未检测到表格或依赖缺失（Camelot={flavor}）。")
            if mode.lower() == "table":
                sys.exit(1)

    if mode.lower() in ("image", "auto", "both"):
        try:
            images_tmp = outdir / "_pages_png"
            images = render_pdf_pages_to_images(pdf_path, pages, dpi, images_tmp)
            out_xlsx_img = build_output_paths(pdf_path, outdir, suffix="_images")
            click.echo(f"[图片嵌入] 渲染 {len(images)} 页，DPI={dpi}。正在写入: {out_xlsx_img}")
            save_images_to_excel(images, out_xlsx_img)
            click.echo(f"[图片嵌入] 完成: {out_xlsx_img}")
        except Exception as e:
            click.echo(f"[图片嵌入] 失败: {e}")
            if mode.lower() in ("image", "auto"):
                sys.exit(3)

    click.echo("完成。")


if __name__ == "__main__":
    try:
        cli()
    except SystemExit:
        raise
    except Exception as exc:
        click.echo("执行失败：" + str(exc))
        traceback.print_exc()
        sys.exit(99)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF格式分析工具
分析PDF中的字体、颜色、表格结构等详细格式信息
"""

import pdfplumber
import json
from pathlib import Path

def analyze_pdf_format(pdf_path, max_pages=5):
    """分析PDF格式信息"""
    format_info = {
        'pages': [],
        'fonts': set(),
        'colors': set(),
        'table_structures': []
    }
    
    with pdfplumber.open(pdf_path) as pdf:
        print(f"分析PDF: {pdf_path}")
        print(f"总页数: {len(pdf.pages)}")
        
        # 分析前几页的格式
        for page_num in range(min(max_pages, len(pdf.pages))):
            page = pdf.pages[page_num]
            print(f"\n=== 第 {page_num + 1} 页 ===")
            
            page_info = {
                'page_num': page_num + 1,
                'chars': [],
                'tables': [],
                'lines': [],
                'rects': []
            }
            
            # 分析字符格式
            chars = page.chars
            print(f"字符数量: {len(chars)}")
            
            if chars:
                # 分析前10个字符的格式
                for i, char in enumerate(chars[:20]):
                    char_info = {
                        'text': char.get('text', ''),
                        'fontname': char.get('fontname', ''),
                        'size': char.get('size', 0),
                        'x0': char.get('x0', 0),
                        'y0': char.get('y0', 0),
                        'x1': char.get('x1', 0),
                        'y1': char.get('y1', 0)
                    }
                    page_info['chars'].append(char_info)
                    
                    # 收集字体信息
                    if char_info['fontname']:
                        format_info['fonts'].add(char_info['fontname'])
                
                print(f"前20个字符: {[c['text'] for c in page_info['chars']]}")
                print(f"字体: {list(set([c['fontname'] for c in page_info['chars'] if c['fontname']]))}")
                print(f"字号: {list(set([c['size'] for c in page_info['chars'] if c['size']]))}")
            
            # 分析表格
            tables = page.extract_tables()
            print(f"表格数量: {len(tables)}")
            
            for table_idx, table in enumerate(tables):
                if table:
                    table_info = {
                        'table_index': table_idx,
                        'rows': len(table),
                        'cols': len(table[0]) if table else 0,
                        'sample_data': table[:3] if len(table) >= 3 else table
                    }
                    page_info['tables'].append(table_info)
                    print(f"表格 {table_idx + 1}: {table_info['rows']}行 x {table_info['cols']}列")
                    print(f"示例数据: {table_info['sample_data']}")
            
            # 分析线条
            lines = page.lines
            print(f"线条数量: {len(lines)}")
            if lines:
                for line in lines[:5]:  # 前5条线
                    line_info = {
                        'x0': line.get('x0', 0),
                        'y0': line.get('y0', 0),
                        'x1': line.get('x1', 0),
                        'y1': line.get('y1', 0),
                        'width': line.get('width', 0),
                        'height': line.get('height', 0)
                    }
                    page_info['lines'].append(line_info)
            
            # 分析矩形
            rects = page.rects
            print(f"矩形数量: {len(rects)}")
            if rects:
                for rect in rects[:5]:  # 前5个矩形
                    rect_info = {
                        'x0': rect.get('x0', 0),
                        'y0': rect.get('y0', 0),
                        'x1': rect.get('x1', 0),
                        'y1': rect.get('y1', 0),
                        'width': rect.get('width', 0),
                        'height': rect.get('height', 0)
                    }
                    page_info['rects'].append(rect_info)
            
            format_info['pages'].append(page_info)
    
    # 转换set为list以便JSON序列化
    format_info['fonts'] = list(format_info['fonts'])
    format_info['colors'] = list(format_info['colors'])
    
    return format_info

def save_format_analysis(format_info, output_file):
    """保存格式分析结果"""
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(format_info, f, ensure_ascii=False, indent=2)
    print(f"\n格式分析结果已保存到: {output_file}")

def main():
    pdf_path = "HK4934 - MEP NSC - S2 (General Area) 2022.11.24.pdf"
    
    if not Path(pdf_path).exists():
        print(f"PDF文件不存在: {pdf_path}")
        return
    
    # 分析PDF格式
    format_info = analyze_pdf_format(pdf_path, max_pages=3)
    
    # 保存分析结果
    save_format_analysis(format_info, "pdf_format_analysis.json")
    
    # 打印总结
    print(f"\n=== 格式分析总结 ===")
    print(f"发现的字体: {format_info['fonts']}")
    print(f"分析的页面数: {len(format_info['pages'])}")
    
    for page_info in format_info['pages']:
        print(f"第{page_info['page_num']}页: {len(page_info['tables'])}个表格, {len(page_info['chars'])}个字符")

if __name__ == "__main__":
    main()

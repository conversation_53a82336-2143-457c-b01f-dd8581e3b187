#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF转Excel工具 - 图形界面版本
保留原始格式，包括表格结构、字体、颜色等
"""

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import threading
import os
from pathlib import Path
from pdf_to_excel import PDFToExcelConverter
from pdf_to_excel_exact_format import PDFToExcelExactConverter


class PDFToExcelGUI:
    """PDF转Excel图形界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("PDF转Excel工具")
        self.root.geometry("600x400")
        self.root.resizable(True, True)
        
        # 设置样式
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        self.converter = PDFToExcelConverter()
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="PDF转Excel转换工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 输入文件选择
        ttk.Label(main_frame, text="选择PDF文件:").grid(row=1, column=0, sticky=tk.W, pady=5)
        
        self.input_path_var = tk.StringVar()
        input_entry = ttk.Entry(main_frame, textvariable=self.input_path_var, width=50)
        input_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 10), pady=5)
        
        input_browse_btn = ttk.Button(main_frame, text="浏览", 
                                     command=self.browse_input_file)
        input_browse_btn.grid(row=1, column=2, pady=5)
        
        # 输出文件选择
        ttk.Label(main_frame, text="输出Excel文件:").grid(row=2, column=0, sticky=tk.W, pady=5)
        
        self.output_path_var = tk.StringVar()
        output_entry = ttk.Entry(main_frame, textvariable=self.output_path_var, width=50)
        output_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 10), pady=5)
        
        output_browse_btn = ttk.Button(main_frame, text="浏览", 
                                      command=self.browse_output_file)
        output_browse_btn.grid(row=2, column=2, pady=5)
        
        # 转换选项
        options_frame = ttk.LabelFrame(main_frame, text="转换选项", padding="10")
        options_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=20)
        options_frame.columnconfigure(0, weight=1)
        
        self.preserve_format_var = tk.BooleanVar(value=True)
        preserve_format_cb = ttk.Checkbutton(options_frame, 
                                           text="保留原始格式（字体、颜色、边框）",
                                           variable=self.preserve_format_var)
        preserve_format_cb.grid(row=0, column=0, sticky=tk.W)
        
        self.auto_adjust_width_var = tk.BooleanVar(value=True)
        auto_adjust_cb = ttk.Checkbutton(options_frame,
                                       text="自动调整列宽",
                                       variable=self.auto_adjust_width_var)
        auto_adjust_cb.grid(row=1, column=0, sticky=tk.W)

        self.exact_format_var = tk.BooleanVar(value=True)
        exact_format_cb = ttk.Checkbutton(options_frame,
                                        text="精确格式匹配（完全保持PDF原始格式）",
                                        variable=self.exact_format_var)
        exact_format_cb.grid(row=2, column=0, sticky=tk.W)
        
        # 转换按钮
        convert_btn = ttk.Button(main_frame, text="开始转换", 
                               command=self.start_conversion,
                               style="Accent.TButton")
        convert_btn.grid(row=4, column=0, columnspan=3, pady=20)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, 
                                          mode='indeterminate')
        self.progress_bar.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        
        # 状态标签
        self.status_var = tk.StringVar(value="准备就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var)
        status_label.grid(row=6, column=0, columnspan=3, pady=5)
        
        # 日志文本框
        log_frame = ttk.LabelFrame(main_frame, text="转换日志", padding="10")
        log_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=20)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(7, weight=1)
        
        self.log_text = tk.Text(log_frame, height=8, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
    def browse_input_file(self):
        """浏览输入文件"""
        filename = filedialog.askopenfilename(
            title="选择PDF文件",
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )
        if filename:
            self.input_path_var.set(filename)
            # 自动设置输出文件名
            if not self.output_path_var.get():
                output_name = Path(filename).stem + "_converted.xlsx"
                output_path = Path(filename).parent / output_name
                self.output_path_var.set(str(output_path))
    
    def browse_output_file(self):
        """浏览输出文件"""
        filename = filedialog.asksaveasfilename(
            title="保存Excel文件",
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.output_path_var.set(filename)
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def start_conversion(self):
        """开始转换"""
        input_path = self.input_path_var.get().strip()
        output_path = self.output_path_var.get().strip()
        
        # 验证输入
        if not input_path:
            messagebox.showerror("错误", "请选择PDF文件")
            return
            
        if not os.path.exists(input_path):
            messagebox.showerror("错误", "PDF文件不存在")
            return
            
        if not output_path:
            messagebox.showerror("错误", "请指定输出文件路径")
            return
        
        # 在新线程中执行转换
        self.conversion_thread = threading.Thread(target=self.convert_file, 
                                                args=(input_path, output_path))
        self.conversion_thread.daemon = True
        self.conversion_thread.start()
    
    def convert_file(self, input_path, output_path):
        """执行文件转换"""
        try:
            # 更新UI状态
            self.root.after(0, lambda: self.status_var.set("正在转换..."))
            self.root.after(0, lambda: self.progress_bar.start())
            self.root.after(0, lambda: self.log_text.delete(1.0, tk.END))
            
            self.root.after(0, lambda: self.log_message(f"开始转换: {input_path}"))

            # 根据选项选择转换器
            if self.exact_format_var.get():
                converter = PDFToExcelExactConverter()
                self.root.after(0, lambda: self.log_message("使用精确格式匹配模式"))
            else:
                converter = PDFToExcelConverter()
                self.root.after(0, lambda: self.log_message("使用标准转换模式"))

            # 执行转换
            result_path = converter.convert(input_path, output_path)
            
            # 转换完成
            self.root.after(0, lambda: self.progress_bar.stop())
            self.root.after(0, lambda: self.status_var.set("转换完成"))
            self.root.after(0, lambda: self.log_message(f"转换成功! 输出文件: {result_path}"))
            
            # 询问是否打开文件
            self.root.after(0, lambda: self.ask_open_file(result_path))
            
        except Exception as e:
            error_msg = f"转换失败: {str(e)}"
            self.root.after(0, lambda: self.progress_bar.stop())
            self.root.after(0, lambda: self.status_var.set("转换失败"))
            self.root.after(0, lambda: self.log_message(error_msg))
            self.root.after(0, lambda: messagebox.showerror("转换失败", error_msg))
    
    def ask_open_file(self, file_path):
        """询问是否打开生成的文件"""
        if messagebox.askyesno("转换完成", "转换完成！是否打开生成的Excel文件？"):
            try:
                os.startfile(file_path)  # Windows
            except AttributeError:
                try:
                    os.system(f'open "{file_path}"')  # macOS
                except:
                    os.system(f'xdg-open "{file_path}"')  # Linux


def main():
    """主函数"""
    root = tk.Tk()
    app = PDFToExcelGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()

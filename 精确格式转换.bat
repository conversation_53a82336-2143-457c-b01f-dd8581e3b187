@echo off
echo ================================
echo PDF转Excel工具 - 精确格式版本
echo 完全保持PDF原始格式
echo ================================
echo.

if "%~1"=="" (
    echo 使用方法: 将PDF文件拖拽到此批处理文件上
    echo 或者: 精确格式转换.bat "PDF文件路径"
    echo.
    pause
    exit /b 1
)

echo 正在转换: %~1
echo 使用精确格式匹配模式...
echo.

python pdf_to_excel_exact_format.py "%~1"

if errorlevel 1 (
    echo.
    echo 转换失败！请检查：
    echo 1. PDF文件是否存在
    echo 2. Python环境是否正确安装
    echo 3. 依赖包是否已安装
    echo.
) else (
    echo.
    echo ================================
    echo 转换成功！
    echo 输出文件已保存在同一目录
    echo ================================
    echo.
)

pause
